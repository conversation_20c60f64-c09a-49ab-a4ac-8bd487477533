@import "tailwindcss";

@custom-variant dark (&:is(.dark *));

/* Warm Cream Theme */
:root {
  --background: hsl(48 33.3333% 97.0588%);
  --foreground: hsl(48 19.6078% 20%);
  --card: hsl(48 33.3333% 97.0588%);
  --card-foreground: hsl(60 2.5641% 7.6471%);
  --popover: hsl(0 0% 100%);
  --popover-foreground: hsl(50.7692 19.4030% 13.1373%);
  --primary: hsl(15.1111 55.5556% 52.3529%);
  --primary-foreground: hsl(0 0% 100%);
  --secondary: hsl(46.1538 22.8070% 88.8235%);
  --secondary-foreground: hsl(50.7692 8.4967% 30.0000%);
  --muted: hsl(44.0000 29.4118% 90%);
  --muted-foreground: hsl(50.0000 2.3622% 50.1961%);
  --accent: hsl(46.1538 22.8070% 88.8235%);
  --accent-foreground: hsl(50.7692 19.4030% 13.1373%);
  --destructive: hsl(60 2.5641% 7.6471%);
  --destructive-foreground: hsl(0 0% 100%);
  --border: hsl(50 7.5000% 84.3137%);
  --input: hsl(50.7692 7.9755% 68.0392%);
  --ring: hsl(15.1111 55.5556% 52.3529%);
  --chart-1: hsl(18.2813 57.1429% 43.9216%);
  --chart-2: hsl(251.4545 84.6154% 74.5098%);
  --chart-3: hsl(46.1538 28.2609% 81.9608%);
  --chart-4: hsl(256.5517 49.1525% 88.4314%);
  --chart-5: hsl(17.7778 60% 44.1176%);
  --sidebar: hsl(51.4286 25.9259% 94.7059%);
  --sidebar-foreground: hsl(60 2.5210% 23.3333%);
  --sidebar-primary: hsl(15.1111 55.5556% 52.3529%);
  --sidebar-primary-foreground: hsl(0 0% 98.4314%);
  --sidebar-accent: hsl(46.1538 22.8070% 88.8235%);
  --sidebar-accent-foreground: hsl(0 0% 20.3922%);
  --sidebar-border: hsl(0 0% 92.1569%);
  --sidebar-ring: hsl(0 0% 70.9804%);
  --font-sans: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  --radius: 0.5rem;
  --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 2px 4px -1px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 4px 6px -1px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 8px 10px -1px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
  --tracking-normal: 0em;
  --spacing: 0.25rem;
}

.dark {
  --background: hsl(60 2.7027% 14.5098%);
  --foreground: hsl(46.1538 9.7744% 73.9216%);
  --card: hsl(60 2.7027% 14.5098%);
  --card-foreground: hsl(48 33.3333% 97.0588%);
  --popover: hsl(60 2.1277% 18.4314%);
  --popover-foreground: hsl(60 5.4545% 89.2157%);
  --primary: hsl(14.7692 63.1068% 59.6078%);
  --primary-foreground: hsl(0 0% 100%);
  --secondary: hsl(48 33.3333% 97.0588%);
  --secondary-foreground: hsl(60 2.1277% 18.4314%);
  --muted: hsl(60 3.8462% 10.1961%);
  --muted-foreground: hsl(51.4286 8.8608% 69.0196%);
  --accent: hsl(48 10.6383% 9.2157%);
  --accent-foreground: hsl(51.4286 25.9259% 94.7059%);
  --destructive: hsl(0 84.2365% 60.1961%);
  --destructive-foreground: hsl(0 0% 100%);
  --border: hsl(60 5.0847% 23.1373%);
  --input: hsl(52.5000 5.1282% 30.5882%);
  --ring: hsl(14.7692 63.1068% 59.6078%);
  --chart-1: hsl(18.2813 57.1429% 43.9216%);
  --chart-2: hsl(251.4545 84.6154% 74.5098%);
  --chart-3: hsl(48 10.6383% 9.2157%);
  --chart-4: hsl(248.2759 25.2174% 22.5490%);
  --chart-5: hsl(17.7778 60% 44.1176%);
  --sidebar: hsl(30 3.3333% 11.7647%);
  --sidebar-foreground: hsl(46.1538 9.7744% 73.9216%);
  --sidebar-primary: hsl(0 0% 20.3922%);
  --sidebar-primary-foreground: hsl(0 0% 98.4314%);
  --sidebar-accent: hsl(60 3.4483% 5.6863%);
  --sidebar-accent-foreground: hsl(46.1538 9.7744% 73.9216%);
  --sidebar-border: hsl(0 0% 92.1569%);
  --sidebar-ring: hsl(0 0% 70.9804%);
  --font-sans: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  --radius: 0.5rem;
  --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 2px 4px -1px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 4px 6px -1px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 8px 10px -1px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    background: linear-gradient(135deg, hsl(48 33.3333% 97.0588%) 0%, hsl(46 25% 95%) 100%);
    font-family: var(--font-sans);
  }
}

/* Warm Theme Typography */
.linear-heading {
  font-family: var(--font-sans);
  font-weight: 600;
  letter-spacing: -0.02em;
  line-height: 1.1;
}

.linear-body {
  font-family: var(--font-sans);
  font-weight: 400;
  line-height: 1.6;
  letter-spacing: -0.01em;
}

.linear-mono {
  font-family: var(--font-mono);
  font-weight: 500;
  letter-spacing: -0.01em;
}

/* Navigation - Warm Style */
.linear-nav {
  background: rgba(248, 244, 238, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border);
  box-shadow: var(--shadow-sm);
}

/* Hero Section - Warm */
.linear-hero {
  background: 
    radial-gradient(ellipse at 20% 70%, hsla(15, 55%, 60%, 0.05) 0%, transparent 50%),
    radial-gradient(ellipse at 80% 30%, hsla(46, 25%, 85%, 0.1) 0%, transparent 50%),
    var(--background);
  position: relative;
  overflow: hidden;
}

.linear-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    linear-gradient(90deg, transparent 49.5%, rgba(0, 0, 0, 0.02) 50%, transparent 50.5%),
    linear-gradient(0deg, transparent 49.5%, rgba(0, 0, 0, 0.02) 50%, transparent 50.5%);
  background-size: 60px 60px;
  pointer-events: none;
  opacity: 0.3;
}

/* Cards - Warm Inspired */
.linear-card {
  background: var(--card);
  border: 1px solid var(--border);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow);
}

.linear-card:hover {
  border-color: var(--primary);
  box-shadow: var(--shadow-lg);
  transform: translateY(-1px);
}

/* Buttons - Warm Theme */
.linear-btn-primary {
  background: var(--primary);
  color: var(--primary-foreground);
  border: 1px solid var(--primary);
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow);
}

.linear-btn-primary:hover {
  background: hsl(15.1111 55.5556% 48%);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.linear-btn-secondary {
  background: transparent;
  color: var(--foreground);
  border: 1px solid var(--border);
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-xs);
}

.linear-btn-secondary:hover {
  background: var(--muted);
  border-color: var(--primary);
  color: var(--foreground);
  box-shadow: var(--shadow);
}

/* Tags - Warm Color Usage */
.linear-tag {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  border: 1px solid;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-xs);
}

/* Green - Primary actions and success */
.linear-tag-green {
  background: hsl(120 30% 90%);
  border-color: hsl(120 40% 75%);
  color: hsl(120 60% 25%);
}

.linear-tag-green:hover {
  background: hsl(120 35% 85%);
  border-color: hsl(120 50% 65%);
  box-shadow: var(--shadow);
}

/* Purple - Premium features */
.linear-tag-purple {
  background: hsl(270 40% 90%);
  border-color: hsl(270 50% 75%);
  color: hsl(270 70% 30%);
}

/* Blue - Technical features (used sparingly) */
.linear-tag-blue {
  background: hsl(220 40% 90%);
  border-color: hsl(220 50% 75%);
  color: hsl(220 70% 30%);
}

/* Orange - Results and metrics (used sparingly) */
.linear-tag-orange {
  background: hsl(25 40% 90%);
  border-color: hsl(25 50% 75%);
  color: hsl(25 70% 30%);
}

/* Pink - Case studies (used very sparingly) */
.linear-tag-pink {
  background: hsl(330 40% 90%);
  border-color: hsl(330 50% 75%);
  color: hsl(330 70% 30%);
}

/* Data Visualization - Warm */
.linear-chart {
  background: var(--card);
  border: 1px solid var(--border);
  border-radius: 0.75rem;
  padding: 2rem;
  box-shadow: var(--shadow);
}

.linear-metric {
  font-family: var(--font-mono);
  font-size: 2.5rem;
  font-weight: 600;
  background: linear-gradient(135deg, var(--primary) 0%, hsl(15 55% 45%) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Thin Lines & Engineering Aesthetic - Warm */
.linear-grid {
  background-image: 
    linear-gradient(rgba(0, 0, 0, 0.04) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 0, 0, 0.04) 1px, transparent 1px);
  background-size: 24px 24px;
}

.linear-divider {
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, rgba(0, 0, 0, 0.1) 50%, transparent 100%);
  margin: 3rem 0;
}

.linear-dotted-line {
  border-top: 1px dotted rgba(0, 0, 0, 0.15);
  margin: 1.5rem 0;
}

/* Testimonials - Warm */
.linear-testimonial {
  background: var(--card);
  border-left: 2px solid var(--primary);
  padding: 1.5rem;
  border-radius: 0.5rem;
  position: relative;
  box-shadow: var(--shadow);
  border: 1px solid var(--border);
}

.linear-testimonial::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 2px;
  height: 100%;
  background: linear-gradient(180deg, var(--primary) 0%, hsl(15 55% 45%) 100%);
  box-shadow: 0 0 8px rgba(218, 136, 103, 0.3);
}

/* Team Cards - Warm */
.linear-team-card {
  background: var(--card);
  border: 1px solid var(--border);
  border-radius: 1rem;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow);
}

.linear-team-card:hover {
  border-color: var(--primary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* Lighting Effects - Warm */
.linear-glow {
  position: relative;
}

.linear-glow::before {
  content: '';
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  background: linear-gradient(135deg, rgba(218, 136, 103, 0.2) 0%, transparent 50%, rgba(218, 136, 103, 0.1) 100%);
  border-radius: inherit;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.linear-glow:hover::before {
  opacity: 1;
}

/* Scroll Animations - Refined */
.linear-fade-in {
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.linear-fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Section Separators - Warm */
.linear-section-separator {
  height: 1px;
  background: 
    radial-gradient(ellipse at center, rgba(218, 136, 103, 0.3) 0%, transparent 70%);
  margin: 4rem 0;
  position: relative;
}

.linear-section-separator::before {
  content: '';
  position: absolute;
  top: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 4px;
  background: var(--primary);
  border-radius: 50%;
  box-shadow: 0 0 8px rgba(218, 136, 103, 0.4);
}

/* Performance Optimizations */
.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
  will-change: transform;
}

/* Responsive Design */
@media (max-width: 768px) {
  .linear-metric {
    font-size: 2rem;
  }
  
  .linear-card {
    margin: 0.5rem 0;
  }
  
  .linear-chart {
    padding: 1.5rem;
  }
}