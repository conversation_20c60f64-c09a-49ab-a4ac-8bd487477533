import { Agent } from '@mastra/core'
import { createLLMClient, type LLMConfig } from './llm-providers'
import { webSearchTool } from './tools/web-search-tool'
import { keywordVolumeTool } from './tools/keyword-volume-tool'
import { keywordDifficultyTool } from './tools/keyword-difficulty-tool'

const SEO_SYSTEM_PROMPT = `
You are an expert SEO strategist agent. Your mission is to perform keyword analysis for a client and generate a "Keyword Strategy Report."

You have access to the following tools:
1.  \`webSearch(query)\`: Use this for research, competitive analysis, and generating ideas.
2.  \`get_keyword_volume(keywords)\`: Use this to get monthly search volume for a list of keywords.
3.  \`get_keyword_difficulty(keywords)\`: Use this to get the SEO Keyword Difficulty score (0-100) for a list of keywords.

**IMPORTANT: Work efficiently and complete within 90 seconds. Be strategic about tool usage.**

**Step 1: Quick Research & Keyword Generation**
1.  Do ONE webSearch to understand the client's business and niche.
2.  Based on this research, generate a focused list of 25-30 relevant keywords.

**Step 2: Get Keyword Data**
1.  Call \`get_keyword_volume\` with your keyword list.
2.  Call \`get_keyword_difficulty\` with the same list.
3.  Combine the data.

**Step 3: Analysis & Report**
1.  Calculate Priority Score: \`(Search Volume / (Keyword Difficulty + 1))\`
2.  Filter and sort by priority.
3.  Generate a concise markdown report with:
   - Executive Summary
   - Top 15 Keywords Table (\`Rank\`, \`Keyword\`, \`Volume\`, \`Difficulty\`, \`Priority Score\`)
   - Quick Content Recommendations
   - Next Steps

**Keep it focused and efficient. Quality over quantity.**
`

export function createSEOStrategistAgent(llmConfig?: LLMConfig) {
  const model = llmConfig ? createLLMClient(llmConfig) : createLLMClient({ provider: 'openai', model: 'gpt-4o-mini' })
  
  return new Agent({
    name: 'SEO Strategist',
    instructions: SEO_SYSTEM_PROMPT,
    model,
    tools: { 
      webSearch: webSearchTool,
      get_keyword_volume: keywordVolumeTool,
      get_keyword_difficulty: keywordDifficultyTool
    }
  })
}

// Default instance with OpenAI
export const seoStrategistAgent = createSEOStrategistAgent()
