export type ThemeType = 'NEO' | 'PROFESSIONAL';

export const NEO_THEME = `
/* NEO Brutalist Theme */
:root {
  --background: hsl(0 0% 100%);
  --foreground: hsl(0 0% 0%);
  --card: hsl(0 0% 100%);
  --card-foreground: hsl(0 0% 0%);
  --popover: hsl(0 0% 100%);
  --popover-foreground: hsl(0 0% 0%);
  --primary: hsl(0 100% 60%);
  --primary-foreground: hsl(0 0% 100%);
  --secondary: hsl(47.9 95.8% 53.1%);
  --secondary-foreground: hsl(0 0% 0%);
  --muted: hsl(0 0% 94.1176%);
  --muted-foreground: hsl(0 0% 20%);
  --accent: hsl(260.1077 108.4472% 67.2897%);
  --accent-foreground: hsl(0 0% 100%);
  --destructive: hsl(0 0% 0%);
  --destructive-foreground: hsl(0 0% 100%);
  --border: hsl(0 0% 0%);
  --input: hsl(0 0% 0%);
  --ring: hsl(0 100% 60%);
  --chart-1: hsl(0 100% 60%);
  --chart-2: hsl(60 100% 50%);
  --chart-3: hsl(216 100% 50%);
  --chart-4: hsl(120 100% 40%);
  --chart-5: hsl(300 100% 40%);
  --sidebar: hsl(0 0% 94.1176%);
  --sidebar-foreground: hsl(0 0% 0%);
  --sidebar-primary: hsl(0 100% 60%);
  --sidebar-primary-foreground: hsl(0 0% 100%);
  --sidebar-accent: hsl(216 100% 50%);
  --sidebar-accent-foreground: hsl(0 0% 100%);
  --sidebar-border: hsl(0 0% 0%);
  --sidebar-ring: hsl(0 100% 60%);
  --font-sans: Open Sans, sans-serif;
  --font-serif: Open Sans, sans-serif;
  --font-mono: Open Sans, sans-serif;
  --radius: 0px;
  --shadow-2xs: 4px 4px 0px 0px hsl(0 0% 0% / 0.50);
  --shadow-xs: 4px 4px 0px 0px hsl(0 0% 0% / 0.50);
  --shadow-sm: 4px 4px 0px 0px hsl(0 0% 0% / 1.00), 4px 1px 2px -1px hsl(0 0% 0% / 1.00);
  --shadow: 4px 4px 0px 0px hsl(0 0% 0% / 1.00), 4px 1px 2px -1px hsl(0 0% 0% / 1.00);
  --shadow-md: 4px 4px 0px 0px hsl(0 0% 0% / 1.00), 4px 2px 4px -1px hsl(0 0% 0% / 1.00);
  --shadow-lg: 4px 4px 0px 0px hsl(0 0% 0% / 1.00), 4px 4px 6px -1px hsl(0 0% 0% / 1.00);
  --shadow-xl: 4px 4px 0px 0px hsl(0 0% 0% / 1.00), 4px 8px 10px -1px hsl(0 0% 0% / 1.00);
  --shadow-2xl: 4px 4px 0px 0px hsl(0 0% 0% / 2.50);
  --tracking-normal: 0.05em;
  --spacing: 0.25rem;
  --hover-transform: translate(-4px, -4px);
}

.dark {
  --background: hsl(0 0% 0%);
  --foreground: hsl(0 0% 100%);
  --card: hsl(0 0% 20%);
  --card-foreground: hsl(0 0% 100%);
  --popover: hsl(0 0% 20%);
  --popover-foreground: hsl(0 0% 100%);
  --primary: hsl(0 100.0000% 70%);
  --primary-foreground: hsl(0 0% 0%);
  --secondary: hsl(47.9 95.8% 53.1%);
  --secondary-foreground: hsl(0 0% 0%);
  --muted: hsl(0 0% 20%);
  --muted-foreground: hsl(0 0% 80%);
  --accent: hsl(210 100% 60%);
  --accent-foreground: hsl(0 0% 0%);
  --destructive: hsl(0 0% 100%);
  --destructive-foreground: hsl(0 0% 0%);
  --border: hsl(0 0% 100%);
  --input: hsl(0 0% 100%);
  --ring: hsl(0 100.0000% 70%);
  --chart-1: hsl(0 100.0000% 70%);
  --chart-2: hsl(60 100% 60%);
  --chart-3: hsl(210 100% 60%);
  --chart-4: hsl(120 60.0000% 50%);
  --chart-5: hsl(300 60.0000% 50%);
  --sidebar: hsl(0 0% 0%);
  --sidebar-foreground: hsl(0 0% 100%);
  --sidebar-primary: hsl(0 100.0000% 70%);
  --sidebar-primary-foreground: hsl(0 0% 0%);
  --sidebar-accent: hsl(210 100% 60%);
  --sidebar-accent-foreground: hsl(0 0% 0%);
  --sidebar-border: hsl(0 0% 100%);
  --sidebar-ring: hsl(0 100.0000% 70%);
  --shadow-2xs: 4px 4px 0px 0px hsl(0 0% 0% / 0.50);
  --shadow-xs: 4px 4px 0px 0px hsl(0 0% 0% / 0.50);
  --shadow-sm: 4px 4px 0px 0px hsl(0 0% 0% / 1.00), 4px 1px 2px -1px hsl(0 0% 0% / 1.00);
  --shadow: 4px 4px 0px 0px hsl(0 0% 0% / 1.00), 4px 1px 2px -1px hsl(0 0% 0% / 1.00);
  --shadow-md: 4px 4px 0px 0px hsl(0 0% 0% / 1.00), 4px 2px 4px -1px hsl(0 0% 0% / 1.00);
  --shadow-lg: 4px 4px 0px 0px hsl(0 0% 0% / 1.00), 4px 4px 6px -1px hsl(0 0% 0% / 1.00);
  --shadow-xl: 4px 4px 0px 0px hsl(0 0% 0% / 1.00), 4px 8px 10px -1px hsl(0 0% 0% / 1.00);
  --shadow-2xl: 4px 4px 0px 0px hsl(0 0% 0% / 2.50);
  --hover-transform: translate(-4px, -4px);
}
`;

export const PROFESSIONAL_THEME = `
/* Warm Cream Professional Theme */
:root {
  /* Warm cream base colors */
  --background: hsl(48 33.3333% 97.0588%);
  --foreground: hsl(48 19.6078% 20%);
  --card: hsl(48 33.3333% 97.0588%);
  --card-foreground: hsl(60 2.5641% 7.6471%);
  --popover: hsl(0 0% 100%);
  --popover-foreground: hsl(50.7692 19.4030% 13.1373%);
  
  /* Warm orange primary */
  --primary: hsl(15.1111 55.5556% 52.3529%);
  --primary-foreground: hsl(0 0% 100%);
  --secondary: hsl(46.1538 22.8070% 88.8235%);
  --secondary-foreground: hsl(50.7692 8.4967% 30.0000%);
  --muted: hsl(44.0000 29.4118% 90%);
  --muted-foreground: hsl(50.0000 2.3622% 50.1961%);
  --accent: hsl(46.1538 22.8070% 88.8235%);
  --accent-foreground: hsl(50.7692 19.4030% 13.1373%);
  --destructive: hsl(60 2.5641% 7.6471%);
  --destructive-foreground: hsl(0 0% 100%);
  
  /* Utility colors */
  --border: hsl(50 7.5000% 84.3137%);
  --input: hsl(50.7692 7.9755% 68.0392%);
  --ring: hsl(15.1111 55.5556% 52.3529%);
  
  /* Chart colors - warm palette */
  --chart-1: hsl(18.2813 57.1429% 43.9216%);
  --chart-2: hsl(251.4545 84.6154% 74.5098%);
  --chart-3: hsl(46.1538 28.2609% 81.9608%);
  --chart-4: hsl(256.5517 49.1525% 88.4314%);
  --chart-5: hsl(17.7778 60% 44.1176%);
  
  /* Sidebar colors */
  --sidebar: hsl(51.4286 25.9259% 94.7059%);
  --sidebar-foreground: hsl(60 2.5210% 23.3333%);
  --sidebar-primary: hsl(15.1111 55.5556% 52.3529%);
  --sidebar-primary-foreground: hsl(0 0% 98.4314%);
  --sidebar-accent: hsl(46.1538 22.8070% 88.8235%);
  --sidebar-accent-foreground: hsl(0 0% 20.3922%);
  --sidebar-border: hsl(0 0% 92.1569%);
  --sidebar-ring: hsl(0 0% 70.9804%);

  /* Typography */
  --font-sans: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
  --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;

  /* Shadows - warm theme */
  --radius: 0.5rem;
  --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 2px 4px -1px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 4px 6px -1px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 8px 10px -1px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);

  /* Spacing and effects */
  --tracking-normal: 0em;
  --spacing: 0.25rem;
  --hover-transform: translate(0, -2px);
}

/* ----------------------  D A R K   M O D E  ------------------------ */
.dark {
  /* Dark warm theme */
  --background: hsl(60 2.7027% 14.5098%);
  --foreground: hsl(46.1538 9.7744% 73.9216%);
  --card: hsl(60 2.7027% 14.5098%);
  --card-foreground: hsl(48 33.3333% 97.0588%);
  --popover: hsl(60 2.1277% 18.4314%);
  --popover-foreground: hsl(60 5.4545% 89.2157%);
  
  /* Dark mode warm orange primary */
  --primary: hsl(14.7692 63.1068% 59.6078%);
  --primary-foreground: hsl(0 0% 100%);
  --secondary: hsl(48 33.3333% 97.0588%);
  --secondary-foreground: hsl(60 2.1277% 18.4314%);
  --muted: hsl(60 3.8462% 10.1961%);
  --muted-foreground: hsl(51.4286 8.8608% 69.0196%);
  --accent: hsl(48 10.6383% 9.2157%);
  --accent-foreground: hsl(51.4286 25.9259% 94.7059%);
  --destructive: hsl(0 84.2365% 60.1961%);
  --destructive-foreground: hsl(0 0% 100%);
  
  /* Dark utility colors */
  --border: hsl(60 5.0847% 23.1373%);
  --input: hsl(52.5000 5.1282% 30.5882%);
  --ring: hsl(14.7692 63.1068% 59.6078%);
  
  /* Dark chart colors */
  --chart-1: hsl(18.2813 57.1429% 43.9216%);
  --chart-2: hsl(251.4545 84.6154% 74.5098%);
  --chart-3: hsl(48 10.6383% 9.2157%);
  --chart-4: hsl(248.2759 25.2174% 22.5490%);
  --chart-5: hsl(17.7778 60% 44.1176%);
  
  /* Dark sidebar colors */
  --sidebar: hsl(30 3.3333% 11.7647%);
  --sidebar-foreground: hsl(46.1538 9.7744% 73.9216%);
  --sidebar-primary: hsl(0 0% 20.3922%);
  --sidebar-primary-foreground: hsl(0 0% 98.4314%);
  --sidebar-accent: hsl(60 3.4483% 5.6863%);
  --sidebar-accent-foreground: hsl(46.1538 9.7744% 73.9216%);
  --sidebar-border: hsl(0 0% 92.1569%);
  --sidebar-ring: hsl(0 0% 70.9804%);

  /* Dark shadows */
  --radius: 0.5rem;
  --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 1px 2px -1px hsl(0 0% 0% / 0.10);
  --shadow-md: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 2px 4px -1px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 4px 6px -1px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0 1px 3px 0px hsl(0 0% 0% / 0.10), 0 8px 10px -1px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
  --hover-transform: translate(0, -2px);
}
`;

export function getThemeCSS(theme: ThemeType): string {
  return theme === 'NEO' ? NEO_THEME : PROFESSIONAL_THEME;
}