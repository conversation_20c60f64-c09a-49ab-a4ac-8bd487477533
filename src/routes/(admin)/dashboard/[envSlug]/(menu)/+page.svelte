<script lang="ts">
  import { invalidate } from "$app/navigation"
  import { page } from "$app/stores"
  import * as Alert from "$lib/components/ui/alert"
  import { onMount } from "svelte"

  onMount(async () => {
    invalidate("data:init")
  })
</script>

<svelte:head>
  <title>Account</title>
</svelte:head>

<h1 class="text-4xl font-black mb-8 text-foreground">Dashboard</h1>

<div class="section-muted card-brutal p-8 mb-8">
  <div class="text-center">
    <h1 class="text-3xl font-black text-foreground mb-4">Welcome to{' '}
      <span class="gradient-text-primary">
        Robynn.ai
      </span>
    </h1>
    <p class="text-lg text-muted-foreground mb-6 font-medium">
      Welcome to your pair product marketer agent. We will add more functionality here.
    </p>
    <div class="flex justify-center gap-4">
      <span class="bg-primary text-primary-foreground px-4 py-2 text-sm font-bold border-2 border-border shadow-brutal-sm">AI-Powered</span>
      <span class="bg-accent text-accent-foreground px-4 py-2 text-sm font-bold border-2 border-border shadow-brutal-sm">Product Marketing</span>
    </div>
  </div>
</div>

<!-- AI Agents Section -->
<div class="mb-8">
  <h2 class="text-2xl font-black text-foreground mb-4">AI Marketing Agents</h2>
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
    <a href="/dashboard/{$page.params.envSlug}/researcher" class="card-brutal p-6 transition-all duration-200 hover:-translate-x-1 hover:-translate-y-1 block">
      <div class="space-y-4">
        <div class="flex items-center gap-3">
          <div class="w-12 h-12 bg-primary text-primary-foreground border-2 border-border shadow-brutal-sm flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
            </svg>
          </div>
          <div>
            <h3 class="text-xl font-black text-foreground">Company Researcher</h3>
            <div class="flex items-center gap-2 mt-1">
              <span class="bg-accent text-accent-foreground px-2 py-1 text-xs font-bold border border-border">AI-Powered</span>
              <span class="bg-secondary text-secondary-foreground px-2 py-1 text-xs font-bold border border-border">Real-time</span>
            </div>
          </div>
        </div>
        <p class="text-muted-foreground font-medium">Get comprehensive company research reports with real-time market data and competitive analysis.</p>
        <div class="text-sm text-muted-foreground">
          <p>• Financial performance & metrics</p>
          <p>• Market position & trends</p>
          <p>• Competitive landscape analysis</p>
        </div>
      </div>
    </a>
    
    <div class="card-brutal p-6 opacity-60">
      <div class="space-y-4">
        <div class="flex items-center gap-3">
          <div class="w-12 h-12 bg-muted text-muted-foreground border-2 border-border shadow-brutal-sm flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
          </div>
          <div>
            <h3 class="text-xl font-black text-foreground">Content Agent</h3>
            <span class="bg-muted text-muted-foreground px-2 py-1 text-xs font-bold border border-border">Coming Soon</span>
          </div>
        </div>
        <p class="text-muted-foreground font-medium">Generate SEO-optimized content in your brand voice with AI-powered writing assistance.</p>
      </div>
    </div>
    
    <a href="/dashboard/{$page.params.envSlug}/agent-seo" class="card-brutal p-6 transition-all duration-200 hover:-translate-x-1 hover:-translate-y-1 block">
      <div class="space-y-4">
        <div class="flex items-center gap-3">
          <div class="w-12 h-12 bg-primary text-primary-foreground border-2 border-border shadow-brutal-sm flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
            </svg>
          </div>
          <div>
            <h3 class="text-xl font-black text-foreground">SEO Strategist</h3>
            <div class="flex items-center gap-2 mt-1">
              <span class="bg-accent text-accent-foreground px-2 py-1 text-xs font-bold border border-border">AI-Powered</span>
              <span class="bg-secondary text-secondary-foreground px-2 py-1 text-xs font-bold border border-border">Keyword Intelligence</span>
            </div>
          </div>
        </div>
        <p class="text-muted-foreground font-medium">Get comprehensive keyword research and SEO strategy recommendations for your business.</p>
        <div class="text-sm text-muted-foreground">
          <p>• Keyword analysis & research</p>
          <p>• Competitive SEO insights</p>
          <p>• Content strategy recommendations</p>
        </div>
      </div>
    </a>
  </div>
</div>

<!-- Marketing Tools Section -->
<div class="mb-8">
  <h2 class="text-2xl font-black text-foreground mb-4">Marketing Tools</h2>
  <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
    <div class="card-brutal p-6">
      <div class="space-y-4">
        <div class="flex items-center gap-3">
          <div class="w-10 h-10 bg-primary text-primary-foreground border-2 border-border shadow-brutal-sm flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
            </svg>
          </div>
          <h2 class="text-xl font-black text-foreground">Smart Insights</h2>
        </div>
        <p class="text-muted-foreground font-medium">Get AI-powered product marketing insights and recommendations.</p>
      </div>
    </div>
    
    <div class="card-brutal p-6">
      <div class="space-y-4">
        <div class="flex items-center gap-3">
          <div class="w-10 h-10 bg-secondary text-secondary-foreground border-2 border-border shadow-brutal-sm flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
          </div>
          <h2 class="text-xl font-black text-foreground">Quick Actions</h2>
        </div>
        <p class="text-muted-foreground font-medium">Access powerful marketing tools and automation features.</p>
      </div>
    </div>
    
    <div class="card-brutal p-6">
      <div class="space-y-4">
        <div class="flex items-center gap-3">
          <div class="w-10 h-10 bg-accent text-accent-foreground border-2 border-border shadow-brutal-sm flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          </div>
          <h2 class="text-xl font-black text-foreground">Analytics</h2>
        </div>
        <p class="text-muted-foreground font-medium">Monitor performance and track your marketing success.</p>
      </div>
    </div>
  </div>
</div>
