import { json } from '@sveltejs/kit'
import type { <PERSON>quest<PERSON><PERSON><PERSON> } from './$types'
import { seoStrategistAgent } from '$lib/agents/seo-strategist'

export const POST: RequestHandler = async ({ request, locals }) => {
  // Verify user is authenticated
  const { session } = locals.auth
  if (!session) {
    return json({ error: 'Unauthorized' }, { status: 401 })
  }

  try {
    const { message } = await request.json()
    
    if (!message || typeof message !== 'string') {
      return json({ error: 'Message is required' }, { status: 400 })
    }

    console.log('SEO agent request received:', message.substring(0, 100) + '...')

    // Generate response using the SEO strategist agent with timeout
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Agent timeout after 90 seconds - try a simpler query')), 90000)
    })

    const agentPromise = seoStrategistAgent.generate([
      {
        role: 'user',
        content: message
      }
    ])

    const response: any = await Promise.race([agentPromise, timeoutPromise])
    
    console.log('SEO agent response generated, length:', response.text?.length || 0)

    return json({ 
      response: response.text
    })
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'
    console.error('Error in SEO strategist agent:', errorMessage)
    
    if (errorMessage.includes('timeout')) {
      return json({ 
        error: 'Request timeout - the analysis is taking too long. Try a shorter, more specific query.',
        suggestion: 'Example: "SEO keywords for fitness coaching" instead of long descriptions'
      }, { status: 408 })
    }
    
    return json({ 
      error: 'An error occurred while processing your request. Please try again with a simpler query.',
      details: errorMessage
    }, { status: 500 })
  }
}
