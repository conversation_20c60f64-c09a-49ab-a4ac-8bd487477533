<script lang="ts">
  import { writable } from "svelte/store"
  import { page } from "$app/stores"
  import { Search, Download, Building2, TrendingUp, FileText, Clock, User, Bo<PERSON> } from "lucide-svelte"

  interface Message {
    id: string
    role: "user" | "assistant"
    content: string
    timestamp: Date
    isReport?: boolean
  }

  const messages = writable<Message[]>([])
  let input = ""
  let isLoading = false
  let selectedMessageId = ""

  function generateId(): string {
    return Math.random().toString(36).substr(2, 9)
  }

  async function sendMessage() {
    if (!input.trim() || isLoading) return

    const userMessage = input.trim()
    const messageId = generateId()
    input = ""
    isLoading = true

    // Add user message to chat
    messages.update((msgs) => [
      ...msgs,
      {
        id: generateId(),
        role: "user",
        content: userMessage,
        timestamp: new Date(),
      },
    ])

    try {
      const response = await fetch(
        `/dashboard/${$page.params.envSlug}/researcher`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ message: userMessage }),
        },
      )

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()

      // Add assistant response to chat
      const assistantMessageId = generateId()
      messages.update((msgs) => [
        ...msgs,
        {
          id: assistantMessageId,
          role: "assistant",
          content: data.response,
          timestamp: new Date(),
          isReport: true,
        },
      ])
      
      selectedMessageId = assistantMessageId
    } catch (error) {
      console.error("Error sending message:", error)
      messages.update((msgs) => [
        ...msgs,
        {
          id: generateId(),
          role: "assistant",
          content:
            "I apologize, but an error occurred while processing your request. Please try again.",
          timestamp: new Date(),
        },
      ])
    } finally {
      isLoading = false
    }
  }

  function handleKeyDown(event: KeyboardEvent) {
    if (event.key === "Enter" && !event.shiftKey) {
      event.preventDefault()
      sendMessage()
    }
  }

  function downloadAsMarkdown(message: Message) {
    const companyName = extractCompanyName(message.content)
    const timestamp = message.timestamp.toISOString().split('T')[0]
    const filename = `${companyName || 'research-report'}-${timestamp}.md`
    
    const markdownContent = `# Company Research Report
**Generated on:** ${message.timestamp.toLocaleDateString()}
**Time:** ${message.timestamp.toLocaleTimeString()}

---

${message.content}

---

*Report generated by Robynn.ai Company Researcher Agent*
`

    const blob = new Blob([markdownContent], { type: 'text/markdown' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = filename
    a.click()
    URL.revokeObjectURL(url)
  }

  function extractCompanyName(content: string): string {
    // Simple extraction - looks for company name in the first line or headers
    const lines = content.split('\n')
    for (const line of lines.slice(0, 5)) {
      if (line.includes('Company:') || line.includes('Company Name:')) {
        return line.split(':')[1]?.trim().replace(/[^a-zA-Z0-9-]/g, '') || ''
      }
      if (line.startsWith('# ') && !line.includes('Research') && !line.includes('Report')) {
        return line.replace('# ', '').trim().replace(/[^a-zA-Z0-9-]/g, '') || ''
      }
    }
    return ''
  }

  function formatContent(content: string): string {
    // Convert markdown-like formatting to HTML for better display
    return content
      .replace(/^# (.*$)/gim, '<h1 class="text-2xl font-bold text-foreground mb-4">$1</h1>')
      .replace(/^## (.*$)/gim, '<h2 class="text-xl font-bold text-foreground mb-3 mt-6">$1</h2>')
      .replace(/^### (.*$)/gim, '<h3 class="text-lg font-bold text-foreground mb-2 mt-4">$1</h3>')
      .replace(/^\*\*(.*?)\*\*/gim, '<strong class="font-bold text-foreground">$1</strong>')
      .replace(/^\* (.*$)/gim, '<li class="ml-4 text-muted-foreground">• $1</li>')
      .replace(/\n\n/gim, '</p><p class="text-muted-foreground leading-relaxed mb-4">')
      .replace(/^(?!<[h|l|s])(.+)$/gim, '<p class="text-muted-foreground leading-relaxed mb-4">$1</p>')
  }
  
  // Auto-scroll to bottom when new messages are added
  $: if ($messages.length > 0) {
    setTimeout(() => {
      const messagesContainer = document.querySelector('.messages-container')
      if (messagesContainer) {
        messagesContainer.scrollTop = messagesContainer.scrollHeight
      }
    }, 100)
  }

  const quickPrompts = [
    "Research Apple Inc. including recent financial performance and market position",
    "Analyze Tesla's business model and competitive advantages", 
    "Provide a comprehensive report on Microsoft's cloud strategy",
    "Research Meta's metaverse investments and future outlook"
  ]
</script>

<svelte:head>
  <title>Company Researcher - AI Agent</title>
</svelte:head>

<style>
  /* Custom scrollbar styles */
  .messages-container {
    scrollbar-width: thin;
    scrollbar-color: var(--border) var(--muted);
  }
  
  .messages-container::-webkit-scrollbar {
    width: 8px;
  }
  
  .messages-container::-webkit-scrollbar-track {
    background: var(--muted);
    border-left: 2px solid var(--border);
  }
  
  .messages-container::-webkit-scrollbar-thumb {
    background: var(--border);
    border-radius: 0;
  }
  
  .messages-container::-webkit-scrollbar-thumb:hover {
    background: var(--foreground);
  }
  
  /* Fixed layout for chat container */
  .chat-container {
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  
  .messages-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 200px; /* Space for input area */
    overflow-y: auto;
    padding: 1.5rem;
  }
  
  .input-wrapper {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 200px;
    border-top: 2px solid var(--border);
    background: var(--card);
  }
</style>

<div class="h-screen flex flex-col" style="background: var(--background);">
  <!-- Header -->
  <div class="border-b-2 flex-shrink-0" style="border-color: var(--border); background: var(--background);">
    <div class="max-w-7xl mx-auto px-6 lg:px-8 py-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <div class="w-12 h-12 flex items-center justify-center border-2" style="background: var(--primary); border-color: var(--border); box-shadow: var(--shadow-sm);">
            <Building2 class="w-6 h-6" style="color: var(--primary-foreground);" />
          </div>
          <div>
            <h1 class="text-3xl font-black" style="color: var(--foreground);">Company Researcher</h1>
            <p class="text-lg font-medium" style="color: var(--muted-foreground);">AI-powered business intelligence and market research</p>
          </div>
        </div>
        <div class="flex items-center space-x-4">
          <div class="flex items-center space-x-2 px-4 py-2 border-2" style="background: var(--accent); border-color: var(--border); box-shadow: var(--shadow-sm);">
            <TrendingUp class="w-4 h-4" style="color: var(--accent-foreground);" />
            <span class="text-sm font-bold" style="color: var(--accent-foreground);">Real-time Data</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="flex-1 overflow-hidden max-w-7xl mx-auto px-6 lg:px-8 py-8 w-full">
    <div class="grid lg:grid-cols-3 gap-8 h-full">
      <!-- Conversation Panel -->
      <div class="lg:col-span-2 h-full">
        <div class="card-brutal p-0 chat-container" style="background: var(--card);">
          <!-- Messages Area -->
          <div class="messages-wrapper messages-container">
            <div class="space-y-6">
              {#if $messages.length === 0}
              <div class="text-center py-12">
                <div class="w-16 h-16 mx-auto mb-4 flex items-center justify-center border-2" style="background: var(--muted); border-color: var(--border);">
                  <Search class="w-8 h-8" style="color: var(--muted-foreground);" />
                </div>
                <h3 class="text-xl font-bold mb-2" style="color: var(--foreground);">Start Your Research</h3>
                <p class="font-medium mb-6" style="color: var(--muted-foreground);">Ask about any company, market, or business topic</p>
                
                <!-- Quick Prompts -->
                <div class="space-y-2">
                  <p class="text-sm font-bold" style="color: var(--muted-foreground);">Try these examples:</p>
                  {#each quickPrompts as prompt}
                    <button 
                      on:click={() => { input = prompt }}
                      class="block w-full text-left p-3 border-2 transition-all duration-200 hover:-translate-x-1 hover:-translate-y-1"
                      style="background: var(--background); border-color: var(--border); box-shadow: var(--shadow-sm);"
                    >
                      <span class="text-sm font-medium" style="color: var(--foreground);">{prompt}</span>
                    </button>
                  {/each}
                </div>
              </div>
            {/if}

            {#each $messages as message}
              <div class="flex gap-4 {message.role === 'user' ? 'flex-row-reverse' : ''}">
                <div class="w-10 h-10 flex-shrink-0 flex items-center justify-center border-2" 
                     style="background: var(--{message.role === 'user' ? 'primary' : 'secondary'}); border-color: var(--border); box-shadow: var(--shadow-sm);">
                  {#if message.role === 'user'}
                    <User class="w-5 h-5" style="color: var(--primary-foreground);" />
                  {:else}
                    <Bot class="w-5 h-5" style="color: var(--secondary-foreground);" />
                  {/if}
                </div>
                
                <div class="flex-1 max-w-3xl">
                  <div class="flex items-center gap-2 mb-2">
                    <span class="text-sm font-bold" style="color: var(--foreground);">
                      {message.role === 'user' ? 'You' : 'Company Researcher'}
                    </span>
                    <div class="flex items-center gap-1">
                      <Clock class="w-3 h-3" style="color: var(--muted-foreground);" />
                      <span class="text-xs" style="color: var(--muted-foreground);">
                        {message.timestamp.toLocaleTimeString()}
                      </span>
                    </div>
                    {#if message.role === 'assistant' && message.isReport}
                      <button 
                        on:click={() => downloadAsMarkdown(message)}
                        class="btn-secondary px-2 py-1 text-xs flex items-center gap-1"
                        title="Download as Markdown"
                      >
                        <Download class="w-3 h-3" />
                        Download
                      </button>
                    {/if}
                  </div>
                  
                  {#if message.role === 'user'}
                    <div class="p-4 border-2" style="background: var(--primary); border-color: var(--border); box-shadow: var(--shadow-sm);">
                      <p class="font-medium" style="color: var(--primary-foreground);">{message.content}</p>
                    </div>
                  {:else}
                    <div class="p-6 border-2" style="background: var(--background); border-color: var(--border); box-shadow: var(--shadow);">
                      <div class="prose prose-sm max-w-none">
                        {@html formatContent(message.content)}
                      </div>
                    </div>
                  {/if}
                </div>
              </div>
            {/each}

            {#if isLoading}
              <div class="flex gap-4">
                <div class="w-10 h-10 flex-shrink-0 flex items-center justify-center border-2" 
                     style="background: var(--secondary); border-color: var(--border); box-shadow: var(--shadow-sm);">
                  <Bot class="w-5 h-5" style="color: var(--secondary-foreground);" />
                </div>
                <div class="flex-1">
                  <div class="flex items-center gap-2 mb-2">
                    <span class="text-sm font-bold" style="color: var(--foreground);">Company Researcher</span>
                    <span class="text-xs" style="color: var(--muted-foreground);">Researching...</span>
                  </div>
                  <div class="p-4 border-2" style="background: var(--background); border-color: var(--border); box-shadow: var(--shadow-sm);">
                    <div class="flex items-center space-x-2">
                      <div class="w-2 h-2 rounded-full animate-pulse" style="background: var(--primary);"></div>
                      <div class="w-2 h-2 rounded-full animate-pulse animation-delay-2000" style="background: var(--primary);"></div>
                      <div class="w-2 h-2 rounded-full animate-pulse animation-delay-4000" style="background: var(--primary);"></div>
                      <span class="text-sm font-medium" style="color: var(--muted-foreground);">Analyzing company data and generating report...</span>
                    </div>
                  </div>
                </div>
              </div>
            {/if}
            </div>
          </div>

          <!-- Input Area -->
          <div class="input-wrapper p-6">
            <div class="flex gap-4">
              <textarea
                bind:value={input}
                on:keydown={handleKeyDown}
                placeholder="Enter company name or research question..."
                class="input-brutal flex-1 resize-none min-h-[100px] p-4"
                disabled={isLoading}
              ></textarea>
              <button
                on:click={sendMessage}
                disabled={!input.trim() || isLoading}
                class="btn-primary px-6 py-4 font-bold flex items-center gap-2 self-end"
              >
                {#if isLoading}
                  <div class="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                {:else}
                  <Search class="w-4 h-4" />
                {/if}
                Research
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Sidebar -->
      <div class="space-y-6 overflow-y-auto">
        <!-- Features -->
        <div class="card-brutal p-6" style="background: var(--card);">
          <h3 class="text-lg font-bold mb-4 flex items-center gap-2" style="color: var(--foreground);">
            <FileText class="w-5 h-5" />
            Research Capabilities
          </h3>
          <div class="space-y-3">
            <div class="flex items-start gap-3">
              <div class="w-2 h-2 rounded-full mt-2" style="background: var(--primary);"></div>
              <div>
                <p class="text-sm font-bold" style="color: var(--foreground);">Company Analysis</p>
                <p class="text-xs" style="color: var(--muted-foreground);">Financial metrics, business model, leadership</p>
              </div>
            </div>
            <div class="flex items-start gap-3">
              <div class="w-2 h-2 rounded-full mt-2" style="background: var(--secondary);"></div>
              <div>
                <p class="text-sm font-bold" style="color: var(--foreground);">Market Intelligence</p>
                <p class="text-xs" style="color: var(--muted-foreground);">Industry trends, competitive landscape</p>
              </div>
            </div>
            <div class="flex items-start gap-3">
              <div class="w-2 h-2 rounded-full mt-2" style="background: var(--accent);"></div>
              <div>
                <p class="text-sm font-bold" style="color: var(--foreground);">Real-time Data</p>
                <p class="text-xs" style="color: var(--muted-foreground);">Latest news, financial updates, events</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Tips -->
        <div class="card-brutal p-6" style="background: var(--card);">
          <h3 class="text-lg font-bold mb-4" style="color: var(--foreground);">Research Tips</h3>
          <div class="space-y-3 text-sm">
            <p style="color: var(--muted-foreground);">• Be specific about what you want to know</p>
            <p style="color: var(--muted-foreground);">• Ask about recent developments or financial performance</p>
            <p style="color: var(--muted-foreground);">• Request comparisons with competitors</p>
            <p style="color: var(--muted-foreground);">• Download reports for offline analysis</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
